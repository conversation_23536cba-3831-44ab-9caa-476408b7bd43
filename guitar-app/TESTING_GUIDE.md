# Руководство по тестированию функции выбора аудиоустройств

## Как протестировать новую функциональность

### 1. Запуск приложения
```bash
cd guitar-app
npm start
```

### 2. Переход к приложению "Распознавание нот"
1. Откройте http://localhost:3000 в браузере
2. Нажмите на "Распознавание нот"

### 3. Тестирование выбора устройств

#### Открытие настроек:
1. Нажмите на иконку шестеренки (⚙️) в правом верхнем углу
2. Откроется модальное окно "Настройки микрофона"

#### Проверка списка устройств:
1. В разделе "Аудиоустройство" должен быть выпадающий список
2. Первый пункт должен быть "По умолчанию"
3. Далее должны отображаться все доступные аудиоустройства

#### Выбор устройства:
1. Выберите любое устройство из списка (не "По умолчанию")
2. После выбора должен появиться второй селектор "Вход"
3. В селекторе "Вход" должны отображаться доступные каналы (Вход 1, Вход 2, и т.д.)
4. Под селектором должна отображаться информация "Доступно входов: X"

#### Выбор канала:
1. Выберите любой канал из списка
2. Настройки должны сохраниться автоматически

#### Обновление списка устройств:
1. Нажмите кнопку "🔄 Обновить"
2. Список устройств должен обновиться

### 4. Тестирование сохранения настроек

#### Проверка сохранения:
1. Выберите устройство и канал
2. Закройте модальное окно настроек
3. Обновите страницу (F5)
4. Откройте настройки снова
5. Выбранные устройство и канал должны сохраниться

#### Проверка localStorage:
1. Откройте DevTools (F12)
2. Перейдите в Application > Local Storage
3. Должны быть ключи:
   - `noteRecognition_selectedDevice`
   - `noteRecognition_selectedChannel`
   - `noteRecognition_sensitivity`
   - `noteRecognition_noiseThreshold`

### 5. Тестирование работы с микрофоном

#### Запуск распознавания:
1. Нажмите кнопку "СТАРТ" для начала распознавания
2. Браузер должен запросить разрешение на доступ к микрофону
3. После разрешения должна начаться визуализация звука

#### Проверка выбранного устройства:
1. В консоли браузера (F12 > Console) должны быть сообщения о выбранном устройстве
2. При смене устройства/канала в настройках во время работы должна происходить переинициализация

### 6. Ожидаемое поведение для разных типов устройств

#### Обычные микрофоны:
- Должно отображаться 2 канала (Вход 1, Вход 2)

#### Профессиональные аудиоинтерфейсы:
- **Focusrite Scarlett Solo**: 2 канала
- **Focusrite Scarlett 2i2**: 2 канала  
- **Focusrite Scarlett 4i4**: 4 канала
- **Behringer U-Phoria UM2**: 2 канала
- **Behringer U-Phoria UMC404**: 4 канала
- **PreSonus AudioBox**: 2-4 канала в зависимости от модели

#### Неизвестные устройства:
- Если в названии есть "interface" или "mixer": 4 канала
- Остальные: 2 канала

### 7. Проверка ошибок

#### Отсутствие устройств:
1. Если нет доступных устройств, должно отображаться "Устройства не найдены"

#### Недоступное устройство:
1. Если сохраненное устройство больше не доступно, должен произойти автоматический сброс на "По умолчанию"

#### Ошибки в консоли:
1. Откройте DevTools > Console
2. Не должно быть критических ошибок (красных сообщений)
3. Предупреждения (желтые) допустимы

### 8. Совместимость

#### Браузеры для тестирования:
- ✅ Chrome/Chromium (рекомендуется)
- ✅ Firefox
- ✅ Safari (macOS)
- ⚠️ Edge (может иметь ограничения)

#### Операционные системы:
- ✅ macOS
- ✅ Windows
- ✅ Linux

### 9. Известные ограничения

1. **Количество каналов**: Некоторые браузеры могут не предоставлять точную информацию о количестве каналов
2. **Названия устройств**: Без разрешения на микрофон названия могут быть скрыты
3. **Переключение в реальном времени**: Может потребоваться несколько секунд для переинициализации

### 10. Отладка

#### Если устройства не отображаются:
1. Проверьте разрешения браузера на доступ к микрофону
2. Убедитесь, что устройства подключены и работают
3. Попробуйте обновить список устройств

#### Если каналы не отображаются:
1. Проверьте консоль на ошибки
2. Убедитесь, что устройство поддерживает несколько каналов
3. Попробуйте выбрать другое устройство
