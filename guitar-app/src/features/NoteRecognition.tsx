import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { AudioAnalyzer } from '../shared/utils/AudioAnalyzer';
import AudioVisualizer from '../shared/components/AudioVisualizer';
import { FiSettings } from 'react-icons/fi';
import BackButton from '../shared/ui/back-button';
import GuitarFretboard, { NoteDisplay } from '../shared/components/GuitarFretboard'; // Добавлен импорт NoteDisplay
import { GUITAR_NOTES } from '../constants/musicTheory'; // Импорт нот напрямую из musicTheory
import { AudioInputDevice, getBaseAudioDevices, getChannelsForDevice, createChannelDeviceId, onDeviceChange } from '../shared/utils/audioDevices';
import { Select } from '../shared/ui/select';



const NoteRecognition: React.FC = () => {
  // Состояние для работы с микрофоном
  const [isListening, setIsListening] = useState(false);
  const [microphoneAvailable, setMicrophoneAvailable] = useState(false);
  const [detectedNote, setDetectedNote] = useState<string | null>(null);
  const [frequencyData, setFrequencyData] = useState<Uint8Array | null>(null);
  const [detectedFrequency, setDetectedFrequency] = useState<number | null>(null);
  const [highlightedNotes, setHighlightedNotes] = useState<NoteDisplay[]>([]); // Новое состояние для подсвеченных нот

  // Настройки анализатора
  const [sensitivity, setSensitivity] = useState<number>(0.05);
  const [noiseThreshold, setNoiseThreshold] = useState<number>(35);

  // Состояние для аудиоустройств
  const [audioDevices, setAudioDevices] = useState<AudioInputDevice[]>([]);
  const [selectedDevice, setSelectedDevice] = useState<string | null>(null);
  const [selectedChannel, setSelectedChannel] = useState<number>(1);
  const [availableChannels, setAvailableChannels] = useState<Array<{ index: number; label: string; value: number }>>([]);
  const [isLoadingDevices, setIsLoadingDevices] = useState(false);
  const [isLoadingChannels, setIsLoadingChannels] = useState(false);

  // Состояния для скрываемых блоков
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [showInstructions, setShowInstructions] = useState(false);

  // Референс для аудио анализатора
  const AudioAnalyzerRef = useRef<AudioAnalyzer | null>(null);

  // Функция загрузки аудиоустройств
  const loadAudioDevices = useCallback(async () => {
    try {
      setIsLoadingDevices(true);
      const devices = await getBaseAudioDevices();
      setAudioDevices(devices);

      // Проверяем, доступно ли выбранное устройство
      if (selectedDevice && !devices.some(d => d.deviceId === selectedDevice)) {
        console.warn('Выбранное аудиоустройство больше не доступно, сбрасываем на устройство по умолчанию');
        setSelectedDevice(null);
        setSelectedChannel(1);
        setAvailableChannels([]);
      }
    } catch (error) {
      console.error('Ошибка при загрузке аудиоустройств:', error);
    } finally {
      setIsLoadingDevices(false);
    }
  }, [selectedDevice]);

  // Функция загрузки каналов для выбранного устройства
  const loadChannelsForDevice = useCallback(async (deviceId: string) => {
    try {
      setIsLoadingChannels(true);
      const channels = await getChannelsForDevice(deviceId);
      setAvailableChannels(channels);

      // Если текущий выбранный канал больше не доступен, сбрасываем на первый
      if (selectedChannel > channels.length) {
        setSelectedChannel(1);
      }
    } catch (error) {
      console.error('Ошибка при загрузке каналов:', error);
      setAvailableChannels([
        { index: 1, label: 'Вход 1', value: 1 },
        { index: 2, label: 'Вход 2', value: 2 }
      ]);
    } finally {
      setIsLoadingChannels(false);
    }
  }, [selectedChannel]);

  // Функция для изменения аудиоустройства
  const handleDeviceChange = useCallback(async (deviceId: string | null) => {
    setSelectedDevice(deviceId);

    if (deviceId) {
      // Загружаем каналы для нового устройства
      await loadChannelsForDevice(deviceId);
    } else {
      // Если устройство не выбрано, очищаем каналы
      setAvailableChannels([]);
      setSelectedChannel(1);
    }

    // Если AudioAnalyzer активен, переинициализируем его с новым устройством
    if (AudioAnalyzerRef.current && isListening) {
      try {
        const fullDeviceId = deviceId ? createChannelDeviceId(deviceId, selectedChannel) : null;
        const success = await AudioAnalyzerRef.current.reinitializeWithDevice(fullDeviceId);
        if (success) {
          console.log('AudioAnalyzer переинициализирован с новым устройством');
        } else {
          console.error('Не удалось переинициализировать AudioAnalyzer с новым устройством');
        }
      } catch (error) {
        console.error('Ошибка при переинициализации AudioAnalyzer:', error);
      }
    }
  }, [isListening, selectedChannel, loadChannelsForDevice]);

  // Функция для изменения канала
  const handleChannelChange = useCallback(async (channelNumber: number) => {
    setSelectedChannel(channelNumber);

    // Если AudioAnalyzer активен, переинициализируем его с новым каналом
    if (AudioAnalyzerRef.current && isListening && selectedDevice) {
      try {
        const fullDeviceId = createChannelDeviceId(selectedDevice, channelNumber);
        const success = await AudioAnalyzerRef.current.reinitializeWithDevice(fullDeviceId);
        if (success) {
          console.log('AudioAnalyzer переинициализирован с новым каналом');
        } else {
          console.error('Не удалось переинициализировать AudioAnalyzer с новым каналом');
        }
      } catch (error) {
        console.error('Ошибка при переинициализации AudioAnalyzer:', error);
      }
    }
  }, [isListening, selectedDevice]);

  // Загрузка настроек из localStorage при инициализации
  useEffect(() => {
    try {
      const savedDevice = localStorage.getItem('noteRecognition_selectedDevice');
      if (savedDevice && savedDevice !== 'null') {
        setSelectedDevice(savedDevice);
      }

      const savedChannel = localStorage.getItem('noteRecognition_selectedChannel');
      if (savedChannel) {
        setSelectedChannel(parseInt(savedChannel, 10));
      }

      const savedSensitivity = localStorage.getItem('noteRecognition_sensitivity');
      if (savedSensitivity) {
        setSensitivity(parseFloat(savedSensitivity));
      }

      const savedNoiseThreshold = localStorage.getItem('noteRecognition_noiseThreshold');
      if (savedNoiseThreshold) {
        setNoiseThreshold(parseInt(savedNoiseThreshold, 10));
      }
    } catch (error) {
      console.error('Ошибка при загрузке настроек:', error);
    }
  }, []);

  // Сохранение настроек в localStorage
  useEffect(() => {
    try {
      localStorage.setItem('noteRecognition_selectedDevice', selectedDevice || 'null');
    } catch (error) {
      console.error('Ошибка при сохранении выбранного устройства:', error);
    }
  }, [selectedDevice]);

  useEffect(() => {
    try {
      localStorage.setItem('noteRecognition_selectedChannel', selectedChannel.toString());
    } catch (error) {
      console.error('Ошибка при сохранении выбранного канала:', error);
    }
  }, [selectedChannel]);

  useEffect(() => {
    try {
      localStorage.setItem('noteRecognition_sensitivity', sensitivity.toString());
    } catch (error) {
      console.error('Ошибка при сохранении чувствительности:', error);
    }
  }, [sensitivity]);

  useEffect(() => {
    try {
      localStorage.setItem('noteRecognition_noiseThreshold', noiseThreshold.toString());
    } catch (error) {
      console.error('Ошибка при сохранении порога шума:', error);
    }
  }, [noiseThreshold]);

  // Загружаем каналы при изменении выбранного устройства
  useEffect(() => {
    if (selectedDevice) {
      loadChannelsForDevice(selectedDevice);
    } else {
      setAvailableChannels([]);
    }
  }, [selectedDevice, loadChannelsForDevice]);

  // Проверяем доступность микрофона и загружаем устройства при загрузке компонента
  useEffect(() => {
    const checkMicrophoneAvailability = async () => {
      const available = await AudioAnalyzer.isMicrophoneAvailable();
      setMicrophoneAvailable(available);

      if (available) {
        await loadAudioDevices();
      }
    };

    checkMicrophoneAvailability();

    // Слушаем изменения устройств
    const unsubscribe = onDeviceChange(() => {
      console.log('Список аудиоустройств изменился, обновляем...');
      loadAudioDevices();
    });

    // Очистка ресурсов при размонтировании компонента
    return () => {
      unsubscribe();
      if (AudioAnalyzerRef.current) {
        AudioAnalyzerRef.current.dispose();
        AudioAnalyzerRef.current = null;
      }
    };
  }, [loadAudioDevices]);

  // Обработчик для запуска/остановки прослушивания
  const toggleListening = useCallback(async () => {
    if (isListening) {
      // Останавливаем прослушивание
      if (AudioAnalyzerRef.current) {
        AudioAnalyzerRef.current.stopListening();
      }
      setIsListening(false);
      setDetectedNote(null);
      setFrequencyData(null);
      setDetectedFrequency(null);
    } else {
      // Запускаем прослушивание
      try {
        // Создаем новый экземпляр анализатора
        if (AudioAnalyzerRef.current) {
          AudioAnalyzerRef.current.dispose();
        }
        AudioAnalyzerRef.current = new AudioAnalyzer();

        // Устанавливаем выбранное аудиоустройство с каналом
        if (selectedDevice) {
          const fullDeviceId = createChannelDeviceId(selectedDevice, selectedChannel);
          AudioAnalyzerRef.current.setAudioDevice(fullDeviceId);
        }

        // Инициализируем анализатор
        const initialized = await AudioAnalyzerRef.current.initialize();
        if (initialized) {
          console.log('Анализатор успешно инициализирован');

          // Устанавливаем настройки
          AudioAnalyzerRef.current.setSensitivity(sensitivity);
          AudioAnalyzerRef.current.setNoiseThreshold(noiseThreshold);

          // Запускаем анализ звука
          AudioAnalyzerRef.current.startListening((note, frequency) => {
            // Обработка распознанной ноты
            handleNoteDetected(note, frequency);

            // Обновляем данные для визуализации
            const freqData = AudioAnalyzerRef.current?.getFrequencyData() || null;
            setFrequencyData(freqData ? new Uint8Array(freqData) : null);
          });

          setIsListening(true);
        } else {
          console.error('Не удалось инициализировать микрофон');
          alert('Не удалось получить доступ к микрофону. Проверьте разрешения браузера.');
        }
      } catch (error) {
        console.error('Ошибка при инициализации аудио анализатора:', error);
        alert('Произошла ошибка при инициализации микрофона.');
      }
    }
  }, [isListening, sensitivity, noiseThreshold]);

  // Функция для нормализации нот (бемоли в диезы)
  const normalizeNote = (note: string): string => {
    if (!note || note.length < 2) return note;
    const noteName = note.slice(0, -1); // Имя ноты без октавы
    const octave = note.slice(-1); // Октава

    const flatToSharpMap: { [key: string]: string } = {
      'Ab': 'G#',
      'Bb': 'A#',
      'Db': 'C#',
      'Eb': 'D#',
      'Gb': 'F#',
    };

    if (flatToSharpMap[noteName]) {
      return flatToSharpMap[noteName] + octave;
    }
    // Дополнительно заменяем символ ♯ на #
    return note.replace('♯', '#');
  };

  // Обработчик распознанной ноты
  const handleNoteDetected = useCallback((note: string, frequency: number) => {
    if (!note) {
      setDetectedNote(null);
      setDetectedFrequency(null);
      setHighlightedNotes([]); // Очищаем подсветку, если нота не распознана
      return;
    }

    // Нормализуем распознанную ноту (включая замену ♯ на #)
    const normalizedDetectedNote = normalizeNote(note);

    // Обновляем отображаемую ноту (можно показывать исходную или нормализованную)
    setDetectedNote(note); // Показываем исходную
    setDetectedFrequency(frequency);

    // Вычисляем все позиции для подсвечивания
    const notesToHighlight: NoteDisplay[] = [];
    if (normalizedDetectedNote) {
      for (const noteEntry of GUITAR_NOTES) {
        const parts = noteEntry.split(':'); // Разделяем строку по ':'
        const noteFullName = parts[0];
        // Нормализуем ноту из константы (на всякий случай, хотя там должны быть только #)
        const normalizedEntryNote = normalizeNote(noteFullName);

        // Сравниваем нормализованные полные имена нот (с октавой)
        if (normalizedEntryNote === normalizedDetectedNote) {
          // Проходим по всем частям, начиная со второй (это позиции)
          for (let i = 1; i < parts.length; i++) {
            const pos = parts[i];
            if (pos && pos.length === 3) { // Убедимся, что позиция валидна (3 символа)
              const stringNum = parseInt(pos.charAt(0), 10); // Номер струны (6-1)
              const fretNum = parseInt(pos.slice(1), 10); // Номер лада (00-12)

              if (!isNaN(stringNum) && !isNaN(fretNum)) {
                notesToHighlight.push({
                  id: `${noteFullName}-${stringNum}-${fretNum}`,
                  string: stringNum, // Номер струны из GUITAR_NOTES (6=низкая E) теперь соответствует отображению (6=низкая E внизу)
                  fret: fretNum,
                  isHighlighted: true,
                  label: normalizedDetectedNote.replace(/[0-9]/g, '') // Отображаем нормализованное имя ноты без октавы
                });
              }
            }
          }
          // Не прерываем цикл, чтобы найти все совпадения на грифе
        }
      }
    }
    setHighlightedNotes(notesToHighlight);

  }, []);

  // Удаляем useMemo для highlightedFret, так как он больше не нужен





  // Компонент модального окна настроек
  const SettingsModal = () => {
    if (!showSettingsModal) return null;

    // Обработчик изменения чувствительности
    const handleSensitivityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = parseFloat(e.target.value);
      setSensitivity(newValue);
      // Обновляем настройку в анализаторе, если он активен
      if (AudioAnalyzerRef.current && isListening) {
        AudioAnalyzerRef.current.setSensitivity(newValue);
      }
    };

    // Обработчик изменения порога шума
    const handleNoiseThresholdChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = parseInt(e.target.value, 10);
      setNoiseThreshold(newValue);
      // Обновляем настройку в анализаторе, если он активен
      if (AudioAnalyzerRef.current && isListening) {
        AudioAnalyzerRef.current.setNoiseThreshold(newValue);
      }
    };

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-6 max-w-md w-full max-h-[90vh] overflow-y-auto">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-xl font-bold">Настройки микрофона</h3>
            <button
              onClick={() => setShowSettingsModal(false)}
              className="text-gray-500 hover:text-gray-700 text-xl"
            >
              ✕
            </button>
          </div>

          <div className="mb-3">
            <label className="block text-sm font-medium mb-1">
              Чувствительность: {sensitivity.toFixed(2)}
            </label>
            <input
              type="range"
              min="0.01"
              max="0.5" // Ограничиваем максимум для лучшего контроля
              step="0.01"
              value={sensitivity}
              onChange={handleSensitivityChange} // Используем новый обработчик
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>Меньше</span>
              <span>Больше</span>
            </div>
          </div>

          <div className="mb-3">
            <label className="block text-sm font-medium mb-1">
              Порог шума: {noiseThreshold}
            </label>
            <input
              type="range"
              min="0"
              max="100" // Уменьшаем максимум для более точной настройки
              step="1"
              value={noiseThreshold}
              onChange={handleNoiseThresholdChange} // Используем новый обработчик
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>Тише</span>
              <span>Громче</span>
            </div>
          </div>

          {/* Выбор аудиоустройства */}
          <div className="mb-3">
            <label className="block text-sm font-medium mb-1">
              Аудиоустройство {isLoadingDevices && '(загрузка...)'}
            </label>
            {audioDevices.length > 0 ? (
              <Select
                options={[
                  { label: 'По умолчанию', value: 'default' },
                  ...audioDevices.map(device => ({
                    label: device.displayName || device.label,
                    value: device.deviceId
                  }))
                ]}
                value={selectedDevice || 'default'}
                onValueChange={(value) => handleDeviceChange(value === 'default' ? null : value)}
                placeholder="Выберите устройство"
                disabled={isLoadingDevices}
                className="w-full"
              />
            ) : !isLoadingDevices ? (
              <p className="text-sm text-gray-500">Устройства не найдены</p>
            ) : null}

            {/* Кнопка обновления списка устройств */}
            <div className="mt-2 flex justify-end">
              <button
                onClick={() => {
                  console.log('Обновление списка аудиоустройств...');
                  loadAudioDevices();
                }}
                disabled={isLoadingDevices}
                className="text-xs text-blue-600 hover:text-blue-800 disabled:text-gray-400"
              >
                🔄 Обновить
              </button>
            </div>
          </div>

          {/* Выбор канала аудиоустройства */}
          {selectedDevice && availableChannels.length > 0 && (
            <div className="mb-3">
              <label className="block text-sm font-medium mb-1">
                Вход {isLoadingChannels && '(загрузка...)'}
              </label>
              <Select
                options={availableChannels.map(channel => ({
                  label: channel.label,
                  value: channel.value.toString()
                }))}
                value={selectedChannel.toString()}
                onValueChange={(value) => handleChannelChange(parseInt(value, 10))}
                placeholder="Выберите вход"
                disabled={isLoadingChannels}
                className="w-full"
              />
              <div className="mt-1 text-xs text-gray-500">
                Доступно входов: {availableChannels.length}
              </div>
            </div>
          )}

          <p className="text-xs text-gray-500 mt-4">
            Изменения применяются в реальном времени, если распознавание активно.
          </p>
        </div>
      </div>
    );
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900 p-4 text-gray-900 dark:text-gray-100">
      {/* Back Button and Settings Icon */}
      <BackButton className="absolute top-4 left-4" />

      {/* Settings Button */}
      <button
        onClick={() => setShowSettingsModal(true)}
        className="inline-flex items-center justify-center p-2 rounded-full bg-blue-600 hover:bg-blue-700 text-white absolute top-4 right-4"
        aria-label="Настройки"
      >
        <FiSettings size={20} />
      </button>

      {/* Title */}
      <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 md:mb-6 text-center text-gray-900 dark:text-white">
        Распознавание нот
      </h1>

      {/* Settings Modal */}
      <SettingsModal />

      <div className="w-full max-w-4xl mx-auto">
        {/* Note Display */}
        <div className="mb-6 text-center">
          <div className="w-28 h-28 mx-auto bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-md flex items-center justify-center rounded-full">
            <span className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white">
              {detectedNote || '?'}
            </span>
          </div>

          {detectedFrequency && (
            <p className="text-xs mt-1">
              Частота: {detectedFrequency.toFixed(2)} Гц
            </p>
          )}
        </div>

        {/* Audio Visualizer */}
        <div className="mb-6">
          <AudioVisualizer
            frequencyData={frequencyData}
            detectedNote={detectedNote || ''} // Передаем пустую строку, если нота не определена
            isListening={isListening}
          />
        </div>

        {/* Guitar Fretboard */}
        <div className="mb-6 w-full max-w-3xl mx-auto">
          {/* Передаем массив подсвеченных нот */}
          <GuitarFretboard notes={highlightedNotes} animate={true} />
        </div>

        {/* Start/Stop Button */}
        <div className="text-center mb-6">
          <button
            onClick={toggleListening}
            disabled={!microphoneAvailable}
            className={`inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-full transition-colors
              ${!microphoneAvailable
                ? 'opacity-50 cursor-not-allowed bg-gray-400 text-white'
                : isListening
                  ? 'bg-red-600 hover:bg-red-700 text-white'
                  : 'bg-green-600 hover:bg-green-700 text-white'}`}
          >
            {isListening ? 'Остановить' : 'Начать распознавание'}
          </button>

          {!microphoneAvailable && (
            <p className="text-red-500 text-sm mt-2">
              Микрофон недоступен. Проверьте разрешения браузера.
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default NoteRecognition;
