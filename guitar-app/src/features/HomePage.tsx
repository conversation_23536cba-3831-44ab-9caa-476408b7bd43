import React from 'react';
import { Link } from 'react-router-dom';
import ThemeToggle from '../shared/ui/theme-toggle';

const HomePage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-white flex flex-col items-center justify-center p-4 relative font-sans">
      {/* Переключатель темы */}
      <div className="absolute top-4 right-4">
        <ThemeToggle />
      </div>
      <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-center mb-8 text-gray-900 dark:text-white">
        Музыкальные тренажеры
      </h1>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 max-w-6xl w-full">
        {/* Карточка "Music Theory" */}
        <Link
          to="/music-theory"
          className="rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden border bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
        >
          <div className="p-6 flex flex-col h-full">
            <div className="rounded-lg p-4 mb-4 flex-grow bg-cyan-100 dark:bg-cyan-900/30">
              <div className="w-full h-32 rounded-lg flex items-center justify-center mb-4 bg-cyan-200 dark:bg-cyan-800/50">
                <span className="text-4xl font-bold text-cyan-800 dark:text-cyan-300">♪♯♭</span>
              </div>
            </div>
            <h2 className="text-xl font-semibold text-center mb-2 text-gray-900 dark:text-gray-100">Music Theory</h2>
            <p className="text-sm text-center text-gray-600 dark:text-gray-400">
              Интерактивный справочник по музыкальной теории
            </p>
          </div>
        </Link>

        {/* Карточка "Ноты и интервалы" */}
        <Link
          to="/guitar-fretboard"
          className="rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden border bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
        >
          <div className="p-6 flex flex-col h-full">
            <div className="rounded-lg p-4 mb-4 flex-grow bg-yellow-100 dark:bg-yellow-900/30">
              <div className="w-full h-32 rounded-lg flex items-center justify-center mb-4 relative bg-yellow-200 dark:bg-yellow-800/50">
                <span className="text-4xl font-bold absolute left-4 text-yellow-800 dark:text-yellow-300">C#</span>
                <span className="text-4xl font-bold absolute right-4 text-amber-800 dark:text-amber-300">G</span>
                <span className="text-2xl font-bold text-gray-600 dark:text-gray-400">↔</span>
              </div>
            </div>
            <h2 className="text-xl font-semibold text-center mb-2 text-gray-900 dark:text-gray-100">Ноты и интервалы</h2>
            <p className="text-sm text-center text-gray-600 dark:text-gray-400">
              Тренажер для изучения нот и интервалов на гитарном грифе
            </p>
          </div>
        </Link>

        {/* Карточка "Tonal Pitch Trainer" */}
        <Link
          to="/pitch-trainer"
          className="rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden border bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
        >
          <div className="p-6 flex flex-col h-full">
            <div className="rounded-lg p-4 mb-4 flex-grow bg-blue-100 dark:bg-blue-900/30">
              <div className="w-full h-32 rounded-lg flex items-center justify-center mb-4 bg-blue-200 dark:bg-blue-800/50">
                <span className="text-4xl font-bold text-blue-800 dark:text-blue-300">♪</span>
              </div>

            </div>
            <h2 className="text-xl font-semibold text-center mb-2 text-gray-900 dark:text-gray-100">Tonal Pitch Trainer</h2>
            <p className="text-sm text-center text-gray-600 dark:text-gray-400">
              Тренажер для развития тонального слуха
            </p>
          </div>
        </Link>

        {/* Карточка "Guitar Pitch Trainer" */}
        <Link
          to="/guitar-pitch-trainer"
          className="rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden border bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
        >
          <div className="p-6 flex flex-col h-full">
            <div className="rounded-lg p-4 mb-4 flex-grow bg-orange-100 dark:bg-orange-900/30">
              <div className="w-full h-32 rounded-lg flex items-center justify-center mb-4 bg-orange-200 dark:bg-orange-800/50">
                <span className="text-4xl font-bold text-orange-800 dark:text-orange-300">♪♫</span>
              </div>

            </div>
            <h2 className="text-xl font-semibold text-center mb-2 text-gray-900 dark:text-gray-100">Guitar Pitch Trainer</h2>
            <p className="text-sm text-center text-gray-600 dark:text-gray-400">
              Тренировка слуха и распознавания нот
            </p>
          </div>
        </Link>

        {/* Карточка "Распознавание нот" */}
        <Link
          to="/note-recognition"
          className="rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden border bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
        >
          <div className="p-6 flex flex-col h-full">
            <div className="rounded-lg p-4 mb-4 flex-grow bg-green-100 dark:bg-green-900/30">
              <div className="w-full h-32 rounded-lg flex items-center justify-center mb-4 bg-green-200 dark:bg-green-800/50">
                <span className="text-4xl font-bold text-green-800 dark:text-green-300">E</span>
              </div>

            </div>
            <h2 className="text-xl font-semibold text-center mb-2 text-gray-900 dark:text-gray-100">Распознавание нот</h2>
            <p className="text-sm text-center text-gray-600 dark:text-gray-400">
              Инструмент для распознавания нот с помощью микрофона
            </p>
          </div>
        </Link>

        {/* Карточка "Chord Trainer" */}
        <Link
          to="/chord-trainer"
          className="rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden border bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
        >
          <div className="p-6 flex flex-col h-full">
            <div className="rounded-lg p-4 mb-4 flex-grow bg-purple-100 dark:bg-purple-900/30">
              <div className="w-full h-32 rounded-lg flex items-center justify-center mb-4 bg-purple-200 dark:bg-purple-800/50">
                <span className="text-4xl font-bold text-purple-800 dark:text-purple-300">Am⁷</span>
              </div>

            </div>
            <h2 className="text-xl font-semibold text-center mb-2 text-gray-900 dark:text-gray-100">Тренажер аккордов</h2>
            <p className="text-sm text-center text-gray-600 dark:text-gray-400">
              Тренажер для изучения и практики аккордов на гитаре
            </p>
          </div>
        </Link>

        {/* Карточка "Name The Chord" */}
        <Link
          to="/name-the-chord"
          className="rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden border bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
        >
          <div className="p-6 flex flex-col h-full">
            <div className="rounded-lg p-4 mb-4 flex-grow bg-red-100 dark:bg-red-900/30">
              <div className="w-full h-32 rounded-lg flex items-center justify-center mb-4 bg-red-200 dark:bg-red-800/50">
                <span className="text-4xl font-bold text-red-800 dark:text-red-300">Em⁷</span>
              </div>

            </div>
            <h2 className="text-xl font-semibold text-center mb-2 text-gray-900 dark:text-gray-100">Назови аккорд</h2>
            <p className="text-sm text-center text-gray-600 dark:text-gray-400">
              Тренажер для запоминания названий аккордов на гитаре
            </p>
          </div>
        </Link>


        {/* Карточка "Score Counter" */}
        <Link
          to="/score-counter"
          className="rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden border bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
        >
          <div className="p-6 flex flex-col h-full">
            <div className="rounded-lg p-4 mb-4 flex-grow bg-teal-100 dark:bg-teal-900/30">
              <div className="w-full h-32 rounded-lg flex items-center justify-center mb-4 bg-teal-200 dark:bg-teal-800/50">
                <span className="text-4xl font-bold text-teal-800 dark:text-teal-300">100</span>
              </div>

            </div>
            <h2 className="text-xl font-semibold text-center mb-2 text-gray-900 dark:text-gray-100">Счетчик очков</h2>
            <p className="text-sm text-center text-gray-600 dark:text-gray-400">
              Высокооптимизированный счетчик очков со статистикой
            </p>
          </div>
        </Link>

        {/* Карточка "Guitar Neck Editor" */}
        <Link
          to="/guitar-neck-editor"
          className="rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden border bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
        >
          <div className="p-6 flex flex-col h-full">
            <div className="rounded-lg p-4 mb-4 flex-grow bg-indigo-100 dark:bg-indigo-900/30">
              <div className="w-full h-32 rounded-lg flex items-center justify-center mb-4 bg-indigo-200 dark:bg-indigo-800/50">
                <span className="text-4xl font-bold text-indigo-800 dark:text-indigo-300">♯</span>
              </div>

            </div>
            <h2 className="text-xl font-semibold text-center mb-2 text-gray-900 dark:text-gray-100">Редактор грифа</h2>
            <p className="text-sm text-center text-gray-600 dark:text-gray-400">
              Создание и редактирование диаграмм гитарного грифа
            </p>
          </div>
        </Link>

        {/* Карточка "Генератор аккордов" */}
        <Link
          to="/midi-chord-generator"
          className="rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden border bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
        >
          <div className="p-6 flex flex-col h-full">
            <div className="rounded-lg p-4 mb-4 flex-grow bg-pink-100 dark:bg-pink-900/30">
              <div className="w-full h-32 rounded-lg flex items-center justify-center mb-4 bg-pink-200 dark:bg-pink-800/50">
                <span className="text-4xl font-bold text-pink-800 dark:text-pink-300">C♯m⁷</span>
              </div>

            </div>
            <h2 className="text-xl font-semibold text-center mb-2 text-gray-900 dark:text-gray-100">Генератор аккордов</h2>
            <p className="text-sm text-center text-gray-600 dark:text-gray-400">
              Тренажер для изучения аккордов с настраиваемыми параметрами
            </p>
          </div>
        </Link>


      </div>

      {/* Раздел "Старые версии" */}
      <div className="mt-12 w-full max-w-6xl">
        <h2 className="text-2xl font-bold text-center mb-6 text-gray-900 dark:text-white">
          Старые версии компонентов
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {/* Старая версия "Тренажер аккордов" */}
          <Link
            to="/chord-trainer-old"
            className="rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden border bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 p-4 flex items-center"
          >
            <div className="w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center mr-3">
              <span className="text-lg font-bold text-purple-800 dark:text-purple-300">A</span>
            </div>
            <div>
              <h3 className="font-medium text-gray-900 dark:text-gray-100">Тренажер аккордов (старый)</h3>
              <p className="text-xs text-gray-600 dark:text-gray-400">Без shadcn/ui</p>
            </div>
          </Link>

          {/* Старая версия "Назови аккорд" */}
          <Link
            to="/name-the-chord-old"
            className="rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden border bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 p-4 flex items-center"
          >
            <div className="w-10 h-10 rounded-full bg-red-100 dark:bg-red-900/30 flex items-center justify-center mr-3">
              <span className="text-lg font-bold text-red-800 dark:text-red-300">E</span>
            </div>
            <div>
              <h3 className="font-medium text-gray-900 dark:text-gray-100">Назови аккорд (старый)</h3>
              <p className="text-xs text-gray-600 dark:text-gray-400">Без shadcn/ui</p>
            </div>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default HomePage;
