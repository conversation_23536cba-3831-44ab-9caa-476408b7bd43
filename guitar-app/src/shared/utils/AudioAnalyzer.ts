/**
 * AudioAnalyzer - утилита для анализа звука и определения нот
 *
 * Использует Web Audio API для захвата звука с микрофона и анализа частот
 * для определения нот, сыгранных на гитаре.
 * Использует McLeod Pitch Method (MPM) через библиотеку pitchy для определения основной частоты.
 */
import { PitchDetector } from 'pitchy';
import { FREQUENCY_TOLERANCE, frequencyToNote } from '../../constants/musicTheory';
import { createAudioConstraints, getAudioInputDevices, getBaseDeviceId, getChannelNumber } from './audioDevices';

export class AudioAnalyzer {
  private audioContext: AudioContext | null = null;
  private analyser: AnalyserNode | null = null;
  private microphone: MediaStreamAudioSourceNode | null = null;
  private timeDataArray!: Float32Array; // Массив для данных временной области (для pitchy)
  private pitchDetector!: ReturnType<typeof PitchDetector.forFloat32Array>; // Экземпляр детектора pitchy
  private stream: MediaStream | null = null;
  private isListening: boolean = false;
  private onNoteDetected: ((note: string, frequency: number) => void) | null = null;
  private animationFrameId: number | null = null;
  private selectedDeviceId: string | null = null; // ID выбранного аудиоустройства
  private selectedChannel: number | null = null; // Номер выбранного канала
  // Чувствительность (минимальная четкость от pitchy) - значение от 0 до 1
  // Более высокое значение требует более четкого сигнала
  private clarityThreshold: number = 0.9; // Порог четкости (экспериментальное значение)
  private lastFrequency: number = -1; // Последняя обнаруженная частота
  private stableFrequencyCounter: number = 0; // Счетчик стабильности частоты
  private readonly STABILITY_THRESHOLD: number = 3; // Минимальное количество стабильных измерений (уменьшено для MPM)
  private frequencyHistory: number[] = []; // История последних обнаруженных частот
  private readonly FREQUENCY_HISTORY_SIZE: number = 3; // Размер буфера истории частот (уменьшено для MPM)
  private readonly MIN_FREQUENCY = 75; // Минимальная частота для гитары (E2 - ~82Hz, но берем с запасом)
  private readonly MAX_FREQUENCY = 700; // Максимальная частота для гитары (E5 - ~659Hz, но берем с запасом)

  /**
   * Инициализирует аудио контекст и запрашивает доступ к микрофону
   */
  public static async isMicrophoneAvailable(): Promise<boolean> {
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      console.warn('MediaDevices API или getUserMedia не поддерживается');
      return false;
    }
    try {
      // Попытка запросить доступ без создания потока (может не работать во всех браузерах)
      // Более надежный способ - просто проверить наличие API
      // const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      // stream.getTracks().forEach(track => track.stop());
      // return true;

      // Проверяем, есть ли аудиоустройства ввода
      const audioDevices = await getAudioInputDevices();
      if (audioDevices.length === 0) {
        console.warn('Аудиоустройства ввода не найдены.');
        return false;
      }

      // Дополнительно можно проверить статус разрешения, если API поддерживается
      if (navigator.permissions && navigator.permissions.query) {
        try {
          const permissionStatus = await navigator.permissions.query({ name: 'microphone' as PermissionName });
          // 'granted', 'prompt', 'denied'
          if (permissionStatus.state === 'denied') {
            console.warn('Доступ к микрофону запрещен пользователем.');
            return false;
          }
          // Если 'prompt', доступ еще не предоставлен, но API доступен
          // Если 'granted', доступ есть
        } catch (permError) {
          console.warn('Не удалось проверить статус разрешения микрофона:', permError);
          // Продолжаем, так как основная проверка API прошла
        }
      }

      return true; // API доступно и есть устройства
    } catch (error) {
      console.error('Ошибка при проверке доступности микрофона:', error);
      return false;
    }
  }

  /**
   * Устанавливает ID аудиоустройства для использования
   * @param deviceId ID устройства или null для устройства по умолчанию
   */
  public setAudioDevice(deviceId: string | null): void {
    this.selectedDeviceId = deviceId;

    // Извлекаем информацию о канале, если указан
    if (deviceId) {
      const baseDeviceId = getBaseDeviceId(deviceId);
      const channelNumber = getChannelNumber(deviceId);

      this.selectedChannel = channelNumber;

      console.log(`AudioAnalyzer: Установлено аудиоустройство: ${baseDeviceId}${channelNumber ? ` (канал ${channelNumber})` : ''}`);
    } else {
      this.selectedChannel = null;
      console.log('AudioAnalyzer: Установлено устройство по умолчанию');
    }
  }

  /**
   * Получает ID текущего выбранного аудиоустройства
   * @returns ID устройства или null
   */
  public getSelectedDeviceId(): string | null {
    return this.selectedDeviceId;
  }

  /**
   * Получает номер выбранного канала
   * @returns Номер канала или null
   */
  public getSelectedChannel(): number | null {
    return this.selectedChannel;
  }

  /**
   * Получает информацию о выбранном устройстве и канале
   * @returns Объект с информацией об устройстве
   */
  public getDeviceInfo(): { deviceId: string | null; channel: number | null; displayName: string } {
    const deviceId = this.selectedDeviceId;
    const channel = this.selectedChannel;

    let displayName = 'По умолчанию';
    if (deviceId) {
      const baseDeviceId = getBaseDeviceId(deviceId);
      displayName = baseDeviceId;
      if (channel) {
        displayName += ` (Канал ${channel})`;
      }
    }

    return {
      deviceId,
      channel,
      displayName
    };
  }

  /**
   * Инициализирует аудио контекст и запрашивает доступ к микрофону
   */
  public async initialize(): Promise<boolean> {
    try {
      console.log('Начинаем инициализацию AudioAnalyzer...');

      // Проверяем поддержку Web Audio API
      if (!window.AudioContext && !(window as any).webkitAudioContext) {
        console.error('Ваш браузер не поддерживает Web Audio API');
        return false;
      }
      console.log('Web Audio API поддерживается');

      // Проверяем поддержку MediaDevices API
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        console.error('Ваш браузер не поддерживает MediaDevices API');
        return false;
      }
      console.log('MediaDevices API поддерживается');

      console.log('Создание AudioContext...');
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      console.log('AudioContext создан:', this.audioContext.state);

      // Если контекст в состоянии suspended, пробуем его запустить
      if (this.audioContext.state === 'suspended') {
        try {
          console.log('Пытаемся запустить AudioContext...');
          await this.audioContext.resume();
          console.log('AudioContext запущен:', this.audioContext.state);
        } catch (resumeError) {
          console.warn('Не удалось запустить AudioContext:', resumeError);
          // Продолжаем, так как контекст может быть запущен позже
        }
      }

      // Запрашиваем доступ к микрофону с учетом выбранного устройства
      console.log('Запрашиваем доступ к микрофону...', this.selectedDeviceId ? `Устройство: ${this.selectedDeviceId}` : 'Устройство по умолчанию');

      const constraints = createAudioConstraints(this.selectedDeviceId);
      this.stream = await navigator.mediaDevices.getUserMedia(constraints);

      console.log('Доступ к микрофону получен');

      // Создаем источник звука из микрофона
      this.microphone = this.audioContext.createMediaStreamSource(this.stream);
      console.log('Источник звука создан');

      // Создаем анализатор
      this.analyser = this.audioContext.createAnalyser();
      // Для pitchy рекомендуется fftSize >= 2048
      this.analyser.fftSize = 4096; // Размер FFT (должен быть степенью 2)
      // smoothingTimeConstant не используется для getFloatTimeDomainData
      // this.analyser.smoothingTimeConstant = 0.8;
      console.log('Анализатор создан, fftSize:', this.analyser.fftSize);

      // Подключаем микрофон к анализатору
      this.microphone.connect(this.analyser);
      console.log('Микрофон подключен к анализатору');

      // Создаем массив для данных временной области (для pitchy)
      // Размер должен соответствовать fftSize для getFloatTimeDomainData
      const buffer = new ArrayBuffer(this.analyser.fftSize * 4);
      this.timeDataArray = new Float32Array(buffer);
      this.pitchDetector = PitchDetector.forFloat32Array(this.analyser.fftSize);
      console.log('Буфер для анализа временной области создан, длина:', this.analyser.fftSize);

      console.log('Инициализация аудио анализатора завершена успешно');
      return true;
    } catch (error) {
      console.error('Ошибка при инициализации аудио анализатора:', error);

      // Проверяем тип ошибки для более информативного сообщения
      if (error instanceof DOMException) {
        if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {
          console.error('Пользователь отклонил доступ к микрофону');
        } else if (error.name === 'NotFoundError' || error.name === 'DevicesNotFoundError') {
          console.error('Микрофон не найден');
        } else if (error.name === 'NotReadableError' || error.name === 'TrackStartError') {
          console.error('Микрофон занят другим приложением');
        } else if (error.name === 'OverconstrainedError' || error.name === 'ConstraintNotSatisfiedError') {
          console.error('Невозможно применить указанные ограничения к микрофону');
        } else if (error.name === 'TypeError') {
          console.error('Некорректные параметры для getUserMedia');
        } else {
          console.error('Ошибка доступа к микрофону:', error.name);
        }
      }

      // Очищаем ресурсы в случае ошибки
      this.dispose();
      return false;
    }
  }

  /**
   * Начинает прослушивание и анализ звука
   * @param callback Функция обратного вызова, вызываемая при обнаружении ноты
   */
  public startListening(callback: (note: string, frequency: number) => void): void {
    // Убрана проверка this.dataArray, так как он больше не используется
    if (!this.audioContext || !this.analyser || !this.timeDataArray) {
      console.error('Аудио анализатор не инициализирован');
      return;
    }

    // Возобновляем AudioContext, если он был приостановлен
    if (this.audioContext.state === 'suspended') {
      this.audioContext.resume().catch(err => console.error('Ошибка при возобновлении AudioContext:', err));
    }

    this.onNoteDetected = callback;
    this.isListening = true;
    this.analyzeAudio();
    console.log('Прослушивание начато');
  }

  /**
   * Останавливает прослушивание и анализ звука
   */
  public stopListening(): void {
    this.isListening = false;
    if (this.animationFrameId !== null) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
    console.log('Прослушивание остановлено');
  }

  /**
   * Переинициализирует AudioAnalyzer с новым аудиоустройством
   * @param deviceId ID нового устройства
   * @returns Promise<boolean> - успешность переинициализации
   */
  public async reinitializeWithDevice(deviceId: string | null): Promise<boolean> {
    console.log('Переинициализация с новым устройством:', deviceId);

    // Сохраняем состояние прослушивания
    const wasListening = this.isListening;
    const callback = this.onNoteDetected;

    // Останавливаем текущее прослушивание
    this.stopListening();

    // Освобождаем ресурсы (кроме AudioContext)
    if (this.microphone) {
      this.microphone.disconnect();
      this.microphone = null;
    }

    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
      this.stream = null;
    }

    // Устанавливаем новое устройство
    this.setAudioDevice(deviceId);

    // Переинициализируем
    const success = await this.initialize();

    // Восстанавливаем прослушивание, если оно было активно
    if (success && wasListening && callback) {
      this.startListening(callback);
    }

    return success;
  }

  /**
   * Освобождает ресурсы аудио анализатора
   */
  public dispose(): void {
    this.stopListening();

    if (this.microphone) {
      this.microphone.disconnect();
      this.microphone = null;
    }

    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
      this.stream = null;
    }

    if (this.audioContext) {
      // Закрываем контекст асинхронно, чтобы избежать ошибок в некоторых браузерах
      this.audioContext.close().catch(err => console.error('Ошибка при закрытии AudioContext:', err));
      this.audioContext = null;
    }

    this.analyser = null;
    // this.dataArray = null; // Удалено, так как dataArray больше не используется
    console.log('Ресурсы аудио анализатора освобождены');
  }

  /**
   * Устанавливает порог четкости для определения нот (из pitchy)
   * @param value Значение от 0 до 1 (рекомендуется 0.8 - 0.95)
   */
  public setClarityThreshold(value: number): void {
    this.clarityThreshold = Math.max(0, Math.min(1, value));
    console.log(`Порог четкости установлен: ${this.clarityThreshold}`);
  }

  /**
   * Устанавливает чувствительность (алиас для setClarityThreshold)
   * @param value Значение от 0 до 1 (рекомендуется 0.01 - 0.5)
   */
  public setSensitivity(value: number): void {
    // Инвертируем значение, так как в UI меньшее значение = меньшая чувствительность
    // а в алгоритме меньшее значение clarityThreshold = большая чувствительность
    const clarityValue = 1 - value;
    this.setClarityThreshold(clarityValue);
    console.log(`Чувствительность установлена: ${value}, порог четкости: ${this.clarityThreshold}`);
  }

  /**
   * Устанавливает порог шума
   * @param value Значение от 0 до 100
   */
  public setNoiseThreshold(value: number): void {
    // Пока не используется, но может быть добавлено в будущем
    console.log(`Порог шума установлен: ${value}`);
  }

  /**
   * Возвращает текущие данные частотного спектра
   * @returns Массив данных частотного спектра или null, если анализатор не инициализирован
   */
  public getFrequencyData(): Uint8Array | null {
    if (!this.analyser) {
      return null;
    }

    const dataArray = new Uint8Array(this.analyser.frequencyBinCount);
    this.analyser.getByteFrequencyData(dataArray);
    return dataArray;
  }

  /**
   * Анализирует аудио данные и определяет ноту
   */
  private analyzeAudio(): void {
    if (!this.isListening || !this.analyser || !this.onNoteDetected || !this.audioContext) {
      return;
    }

    // Получаем данные временной области для pitchy
    this.analyser.getFloatTimeDomainData(this.timeDataArray as any);

    // Определяем основную частоту с помощью pitchy
    // Приводим тип для совместимости с pitchy
    const [frequency, clarity] = this.pitchDetector.findPitch(this.timeDataArray as any, this.audioContext.sampleRate);

    // Проверяем четкость и диапазон частоты
    if (clarity > this.clarityThreshold && frequency >= this.MIN_FREQUENCY && frequency <= this.MAX_FREQUENCY) {
      // Частота достаточно четкая и в нужном диапазоне
      const currentFrequency = frequency;

      // Сглаживание и проверка стабильности частоты
      this.frequencyHistory.push(currentFrequency);
      if (this.frequencyHistory.length > this.FREQUENCY_HISTORY_SIZE) {
        this.frequencyHistory.shift(); // Удаляем старое значение
      }

      // Проверяем, достаточно ли стабильна частота
      const isStable = this.frequencyHistory.every(freq =>
        Math.abs(freq - currentFrequency) < (currentFrequency * FREQUENCY_TOLERANCE / 100)
      );

      if (isStable && this.frequencyHistory.length === this.FREQUENCY_HISTORY_SIZE) {
        // Используем среднее значение из истории для большей точности
        const stableFrequency = this.frequencyHistory.reduce((sum, freq) => sum + freq, 0) / this.FREQUENCY_HISTORY_SIZE;

        // Проверяем, изменилась ли стабильная частота значительно
        if (Math.abs(stableFrequency - this.lastFrequency) > (stableFrequency * FREQUENCY_TOLERANCE / 100)) {
          this.stableFrequencyCounter = 0; // Сбрасываем счетчик, если частота изменилась
        } else {
          this.stableFrequencyCounter++; // Увеличиваем счетчик стабильности
        }

        this.lastFrequency = stableFrequency;

        // Если частота стабильна достаточно долго, определяем ноту
        if (this.stableFrequencyCounter >= this.STABILITY_THRESHOLD) {
          const note = this.frequencyToNote(stableFrequency);
          if (note && this.onNoteDetected) {
            // console.log(`Detected Note: ${note}, Frequency: ${stableFrequency.toFixed(2)}, Clarity: ${clarity.toFixed(2)}`);
            this.onNoteDetected(note, stableFrequency);
          }
          // Не сбрасываем счетчик здесь, чтобы нота отображалась, пока она звучит
        }
      } else {
        // Частота нестабильна, сбрасываем счетчик
        this.stableFrequencyCounter = 0;
        // this.lastFrequency = -1; // Не сбрасываем lastFrequency, чтобы избежать мерцания
      }
    } else {
      // Сигнал слишком слабый, нечеткий или вне диапазона, сбрасываем счетчики
      this.stableFrequencyCounter = 0;
      this.lastFrequency = -1;
      this.frequencyHistory = []; // Очищаем историю при отсутствии сигнала
      // Можно вызвать onNoteDetected с пустыми значениями, если нужно обнулять интерфейс
      // if (this.onNoteDetected) {
      //   this.onNoteDetected('', -1);
      // }
    }

    // Продолжаем анализ в следующем кадре
    this.animationFrameId = requestAnimationFrame(() => this.analyzeAudio());
  }

  /**
   * Преобразует частоту в ближайшую ноту
   * @param frequency Частота в Гц
   * @returns Название ноты (например, "A4") или пустую строку, если нота не найдена
   */
  private frequencyToNote(frequency: number): string {
    // Используем функцию из musicTheory.ts
    return frequencyToNote(frequency);
  }
}
