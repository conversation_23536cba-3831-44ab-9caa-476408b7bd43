/**
 * Тесты для функциональности выбора каналов аудиоустройств
 */

import { 
  getBaseDeviceId, 
  getChannelNumber, 
  createAudioConstraints,
  getDeviceChannels
} from '../audioDevices';

describe('Audio Device Channels', () => {
  describe('getBaseDeviceId', () => {
    it('должен возвращать базовый deviceId без канала', () => {
      expect(getBaseDeviceId('device123')).toBe('device123');
      expect(getBaseDeviceId('device123:channel:1')).toBe('device123');
      expect(getBaseDeviceId('device123:channel:2')).toBe('device123');
      expect(getBaseDeviceId('very-long-device-id-12345:channel:4')).toBe('very-long-device-id-12345');
    });

    it('должен обрабатывать пустые строки', () => {
      expect(getBaseDeviceId('')).toBe('');
    });
  });

  describe('getChannelNumber', () => {
    it('должен возвращать номер канала из deviceId', () => {
      expect(getChannelNumber('device123:channel:1')).toBe(1);
      expect(getChannelNumber('device123:channel:2')).toBe(2);
      expect(getChannelNumber('device123:channel:10')).toBe(10);
    });

    it('должен возвращать null для deviceId без канала', () => {
      expect(getChannelNumber('device123')).toBe(null);
      expect(getChannelNumber('device123:something:else')).toBe(null);
      expect(getChannelNumber('')).toBe(null);
    });

    it('должен обрабатывать некорректные номера каналов', () => {
      expect(getChannelNumber('device123:channel:abc')).toBe(null);
      expect(getChannelNumber('device123:channel:')).toBe(null);
    });
  });

  describe('createAudioConstraints', () => {
    it('должен создавать constraints для устройства по умолчанию', () => {
      const constraints = createAudioConstraints(null);
      
      expect(constraints.audio).toBeDefined();
      expect(constraints.video).toBe(false);
      expect((constraints.audio as MediaTrackConstraints).deviceId).toBeUndefined();
    });

    it('должен создавать constraints для конкретного устройства', () => {
      const constraints = createAudioConstraints('device123');
      
      expect(constraints.audio).toBeDefined();
      expect((constraints.audio as MediaTrackConstraints).deviceId).toEqual({ exact: 'device123' });
    });

    it('должен создавать constraints для конкретного канала', () => {
      const constraints = createAudioConstraints('device123:channel:2');
      
      expect(constraints.audio).toBeDefined();
      expect((constraints.audio as MediaTrackConstraints).deviceId).toEqual({ exact: 'device123' });
      expect((constraints.audio as MediaTrackConstraints).channelCount).toEqual({ exact: 1 });
    });

    it('должен обрабатывать первый канал без дополнительных constraints', () => {
      const constraints = createAudioConstraints('device123:channel:1');
      
      expect(constraints.audio).toBeDefined();
      expect((constraints.audio as MediaTrackConstraints).deviceId).toEqual({ exact: 'device123' });
      // Для первого канала не добавляем дополнительные constraints
    });
  });

  describe('getDeviceChannels', () => {
    // Мокаем getUserMedia для тестирования
    const mockGetUserMedia = jest.fn();
    const mockGetCapabilities = jest.fn();
    const mockStop = jest.fn();

    beforeEach(() => {
      // Сбрасываем моки
      mockGetUserMedia.mockReset();
      mockGetCapabilities.mockReset();
      mockStop.mockReset();

      // Мокаем navigator.mediaDevices.getUserMedia
      Object.defineProperty(global.navigator, 'mediaDevices', {
        value: {
          getUserMedia: mockGetUserMedia
        },
        writable: true
      });
    });

    it('должен возвращать информацию о каналах для многоканального устройства', async () => {
      // Мокаем ответ getUserMedia
      const mockTrack = {
        getCapabilities: mockGetCapabilities,
        stop: mockStop
      };
      const mockStream = {
        getAudioTracks: () => [mockTrack],
        getTracks: () => [mockTrack]
      };

      mockGetUserMedia.mockResolvedValue(mockStream);
      mockGetCapabilities.mockReturnValue({
        channelCount: { max: 4 }
      });

      const result = await getDeviceChannels('device123');

      expect(result.maxChannels).toBe(4);
      expect(result.channels).toHaveLength(4);
      expect(result.channels[0]).toEqual({
        index: 1,
        label: 'Вход 1',
        deviceId: 'device123:channel:1'
      });
      expect(result.channels[3]).toEqual({
        index: 4,
        label: 'Вход 4',
        deviceId: 'device123:channel:4'
      });
    });

    it('должен обрабатывать ошибки и возвращать значения по умолчанию', async () => {
      mockGetUserMedia.mockRejectedValue(new Error('Access denied'));

      const result = await getDeviceChannels('device123');

      expect(result.maxChannels).toBe(1);
      expect(result.channels).toHaveLength(1);
      expect(result.channels[0]).toEqual({
        index: 1,
        label: 'Вход 1',
        deviceId: 'device123:channel:1'
      });
    });
  });
});
