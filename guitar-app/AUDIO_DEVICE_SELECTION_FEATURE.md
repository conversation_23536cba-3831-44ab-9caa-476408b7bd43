# Функция выбора аудиоинтерфейса и входа в приложении "Распознавание нот"

## Описание изменений

В приложение "Распознавание нот" добавлена улучшенная функция выбора аудиоинтерфейса и входа с раздельными селекторами, которая позволяет пользователям:

1. **Выбирать конкретное аудиоустройство** из списка доступных микрофонов и аудиоинтерфейсов
2. **Выбирать отдельные входы** многоканальных аудиоинтерфейсов (до 16 каналов)
3. **Использовать раздельные селекторы** для устройства и канала
4. **Сохранять настройки** в localStorage для автоматического восстановления при следующем запуске
5. **Обновлять список устройств** в реальном времени при подключении/отключении устройств

## Добавленная функциональность

### 1. Состояние компонента
- `audioDevices` - список доступных аудиоустройств
- `selectedDevice` - выбранное устройство (ID)
- `selectedChannel` - выбранный канал (номер)
- `availableChannels` - список доступных каналов для выбранного устройства
- `isLoadingDevices` - индикатор загрузки списка устройств
- `isLoadingChannels` - индикатор загрузки списка каналов

### 2. Функции
- `loadAudioDevices()` - загрузка списка доступных устройств
- `loadChannelsForDevice()` - загрузка каналов для конкретного устройства
- `handleDeviceChange()` - обработка изменения выбранного устройства
- `handleChannelChange()` - обработка изменения выбранного канала
- Автоматическое сохранение настроек в localStorage
- Автоматическая загрузка сохраненных настроек при запуске

### 3. UI компоненты
- **Первый Select** для выбора аудиоустройства
- **Второй Select** для выбора канала выбранного устройства
- **Кнопка обновления** списка устройств
- **Индикаторы загрузки** при получении списка устройств и каналов
- **Отображение "По умолчанию"** для системного устройства
- **Счетчик доступных входов** для выбранного устройства

### 4. Интеграция с AudioAnalyzer
- Автоматическая установка выбранного устройства при инициализации
- Переинициализация анализатора при смене устройства в реальном времени
- Поддержка многоканальных аудиоинтерфейсов

## Использование

1. **Открыть настройки**: Нажать на иконку шестеренки в правом верхнем углу
2. **Выбрать устройство**: В разделе "Аудиоустройство" выбрать нужный микрофон или аудиоинтерфейс
3. **Выбрать вход**: После выбора устройства появится второй селектор "Вход" со списком доступных каналов
4. **Обновить список**: При необходимости нажать кнопку "🔄 Обновить" для обновления списка устройств
5. **Применить**: Настройки применяются автоматически и сохраняются

## Технические детали

### Сохранение настроек
Настройки сохраняются в localStorage с ключами:
- `noteRecognition_selectedDevice` - выбранное устройство
- `noteRecognition_selectedChannel` - выбранный канал
- `noteRecognition_sensitivity` - чувствительность
- `noteRecognition_noiseThreshold` - порог шума

### Поддержка многоканальных устройств
Система автоматически определяет количество каналов для каждого устройства:
- **Автоматическое определение**: Использует Web Audio API для получения информации о каналах
- **Эвристика для известных устройств**: Focusrite Scarlett, PreSonus AudioBox, Behringer U-Phoria и др.
- **Поддержка до 16 каналов**: Для профессиональных аудиоинтерфейсов
- **Раздельные селекторы**: Устройство и канал выбираются независимо

### Обработка ошибок
- Автоматический откат на устройство по умолчанию при недоступности выбранного
- Логирование ошибок в консоль
- Graceful handling при отсутствии разрешений на микрофон

## Новые функции в audioDevices.ts

### Добавленные функции:
- `getBaseAudioDevices()` - получение списка устройств без каналов
- `getChannelsForDevice(deviceId)` - получение каналов для конкретного устройства
- `createChannelDeviceId(baseDeviceId, channelNumber)` - создание полного ID с каналом

### Улучшенная функция:
- `getDeviceChannels()` - улучшенное определение количества каналов с эвристикой для известных устройств

## Совместимость

Функция использует существующую инфраструктуру:
- `audioDevices.ts` - утилиты для работы с устройствами (расширены новыми функциями)
- `AudioAnalyzer.ts` - анализатор звука с поддержкой выбора устройств
- `Select` компонент из shared/ui
- localStorage для сохранения настроек

Все изменения обратно совместимы. Старая функция `getAudioInputDevicesWithChannels()` сохранена для совместимости с другими компонентами.
