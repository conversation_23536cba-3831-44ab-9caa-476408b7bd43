# Функция выбора аудиоинтерфейса и входа в приложении "Распознавание нот"

## Описание изменений

В приложение "Распознавание нот" добавлена функция выбора аудиоинтерфейса и входа, которая позволяет пользователям:

1. **Выбирать конкретное аудиоустройство** из списка доступных микрофонов и аудиоинтерфейсов
2. **Выбирать отдельные каналы** многоканальных аудиоинтерфейсов
3. **Сохранять настройки** в localStorage для автоматического восстановления при следующем запуске
4. **Обновлять список устройств** в реальном времени при подключении/отключении устройств

## Добавленная функциональность

### 1. Состояние компонента
- `audioDevices` - список доступных аудиоустройств
- `selectedAudioDevice` - выбранное устройство (ID)
- `isLoadingDevices` - индикатор загрузки списка устройств

### 2. Функции
- `loadAudioDevices()` - загрузка списка доступных устройств с каналами
- `handleAudioDeviceChange()` - обработка изменения выбранного устройства
- Автоматическое сохранение настроек в localStorage
- Автоматическая загрузка сохраненных настроек при запуске

### 3. UI компоненты
- **Select компонент** для выбора аудиоустройства в модальном окне настроек
- **Кнопка обновления** списка устройств
- **Индикатор загрузки** при получении списка устройств
- **Отображение "По умолчанию"** для системного устройства

### 4. Интеграция с AudioAnalyzer
- Автоматическая установка выбранного устройства при инициализации
- Переинициализация анализатора при смене устройства в реальном времени
- Поддержка многоканальных аудиоинтерфейсов

## Использование

1. **Открыть настройки**: Нажать на иконку шестеренки в правом верхнем углу
2. **Выбрать устройство**: В разделе "Аудиоустройство" выбрать нужный микрофон или вход
3. **Обновить список**: При необходимости нажать кнопку "🔄 Обновить"
4. **Применить**: Настройки применяются автоматически и сохраняются

## Технические детали

### Сохранение настроек
Настройки сохраняются в localStorage с ключами:
- `noteRecognition_selectedAudioDevice` - выбранное устройство
- `noteRecognition_sensitivity` - чувствительность
- `noteRecognition_noiseThreshold` - порог шума

### Поддержка многоканальных устройств
Для аудиоинтерфейсов с несколькими входами создаются отдельные опции:
- "Устройство - Вход 1"
- "Устройство - Вход 2"
- и т.д.

### Обработка ошибок
- Автоматический откат на устройство по умолчанию при недоступности выбранного
- Логирование ошибок в консоль
- Graceful handling при отсутствии разрешений на микрофон

## Совместимость

Функция использует существующую инфраструктуру:
- `audioDevices.ts` - утилиты для работы с устройствами
- `AudioAnalyzer.ts` - анализатор звука с поддержкой выбора устройств
- `Select` компонент из shared/ui
- localStorage для сохранения настроек

Все изменения обратно совместимы и не влияют на существующую функциональность.
