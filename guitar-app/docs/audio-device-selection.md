# Выбор аудиоустройств в Guitar Pitch Trainer

## Обзор

Guitar Pitch Trainer поддерживает выбор конкретных аудиоустройств для распознавания нот с микрофона. Эта функция позволяет использовать профессиональные аудиоинтерфейсы, USB-микрофоны и другие специализированные устройства ввода.

## Как использовать

### 1. Включение распознавания нот
1. Откройте Guitar Pitch Trainer
2. Нажмите на иконку настроек (⚙️) в правом верхнем углу
3. Включите переключатель "Распознавание нот с микрофона"

### 2. Выбор аудиоустройства и канала
1. После включения распознавания нот появится выпадающий список "Аудиоустройство"
2. Выберите нужное устройство из списка:
   - **По умолчанию** - системное устройство по умолчанию
   - **Конкретные устройства** - все доступные микрофоны и аудиоинтерфейсы
   - **Отдельные каналы** - для многоканальных устройств отображаются отдельные входы (например, "Focusrite Scarlett 2i2 - Вход 1", "Focusrite Scarlett 2i2 - Вход 2")

### 3. Обновление списка устройств
- Нажмите кнопку "🔄 Обновить" для обновления списка доступных устройств
- Это полезно при подключении/отключении USB-устройств

### 4. Индикатор состояния
- **🎤 Активно** (зеленый) - устройство работает и распознает звук
- **🎤 Неактивно** (серый) - устройство выбрано, но не активно
- **Канал X** (синий) - отображается номер выбранного канала для многоканальных устройств

### 5. Работа с многоканальными устройствами

#### Автоматическое определение каналов
- Приложение автоматически определяет количество доступных каналов для каждого устройства
- Для устройств с несколькими каналами создаются отдельные пункты в списке
- Каждый канал можно выбрать независимо

#### Примеры многоканальных устройств
- **Focusrite Scarlett 2i2**: отображается как "Focusrite Scarlett 2i2 - Вход 1" и "Focusrite Scarlett 2i2 - Вход 2"
- **PreSonus AudioBox USB 96**: "PreSonus AudioBox USB 96 - Вход 1" и "PreSonus AudioBox USB 96 - Вход 2"
- **Zoom PodTrak P4**: отображаются все 4 входа отдельно

#### Преимущества выбора конкретного канала
- Возможность подключить гитару к конкретному входу аудиоинтерфейса
- Изоляция от других источников звука на других каналах
- Оптимальное качество сигнала для каждого инструмента

## Поддерживаемые устройства

### Встроенные микрофоны
- Микрофон ноутбука/компьютера
- Встроенный микрофон веб-камеры

### USB-микрофоны
- Blue Yeti, Blue Snowball
- Audio-Technica ATR2100x-USB
- Rode PodMic USB
- Samson Go Mic

### Аудиоинтерфейсы
- Focusrite Scarlett серии
- PreSonus AudioBox
- Behringer U-Phoria
- Zoom PodTrak серии

### Профессиональные устройства
- RME Babyface
- Universal Audio Apollo
- MOTU M2/M4
- Steinberg UR серии

## Технические особенности

### Автоматическое восстановление
- При недоступности выбранного устройства автоматически переключается на устройство по умолчанию
- Отображает предупреждения в консоли браузера

### Переключение на лету
- Можно менять устройство во время игры
- AudioAnalyzer автоматически переинициализируется с новым устройством

### Сохранение настроек
- Выбранное устройство сохраняется в localStorage
- Автоматически восстанавливается при следующем запуске

## Устранение неполадок

### Устройство не отображается в списке
1. Убедитесь, что устройство подключено и распознается системой
2. Нажмите "🔄 Обновить" для обновления списка
3. Перезагрузите браузер и дайте разрешение на доступ к микрофону

### Нет звука с выбранного устройства
1. Проверьте уровни записи в системных настройках
2. Убедитесь, что устройство не используется другим приложением
3. Попробуйте переключиться на "По умолчанию" и обратно

### Низкое качество распознавания
1. Проверьте настройки чувствительности в AudioAnalyzer
2. Убедитесь, что микрофон направлен на гитару
3. Минимизируйте фоновый шум

### Ошибки разрешений
1. Убедитесь, что браузер имеет разрешение на доступ к микрофону
2. Проверьте настройки приватности в системе
3. Попробуйте использовать HTTPS (для некоторых браузеров)

## API для разработчиков

### Основные функции

```typescript
// Получение списка аудиоустройств с каналами
const devices = await getAudioInputDevicesWithChannels();

// Получение информации о каналах устройства
const channelInfo = await getDeviceChannels(deviceId);

// Проверка доступности устройства
const isAvailable = await isAudioDeviceAvailable(deviceId);

// Тестирование устройства
const isWorking = await testAudioDevice(deviceId);

// Создание constraints для getUserMedia (поддерживает каналы)
const constraints = createAudioConstraints(deviceId); // deviceId может быть "device123:channel:2"

// Извлечение базового deviceId из расширенного
const baseDeviceId = getBaseDeviceId("device123:channel:2"); // вернет "device123"

// Извлечение номера канала
const channelNumber = getChannelNumber("device123:channel:2"); // вернет 2
```

### Использование в AudioAnalyzer

```typescript
const analyzer = new AudioAnalyzer();

// Установка устройства (поддерживает каналы)
analyzer.setAudioDevice("device123:channel:2"); // Канал 2 устройства device123
analyzer.setAudioDevice("device123"); // Все каналы устройства device123

// Инициализация с выбранным устройством
await analyzer.initialize();

// Переключение устройства на лету
await analyzer.reinitializeWithDevice("device456:channel:1");

// Получение информации о выбранном устройстве
const deviceInfo = analyzer.getDeviceInfo();
console.log(deviceInfo.displayName); // "Device Name (Канал 2)"
console.log(deviceInfo.channel); // 2

// Получение номера канала
const channel = analyzer.getSelectedChannel(); // 2 или null
```

## Совместимость браузеров

- ✅ Chrome 66+
- ✅ Firefox 60+
- ✅ Safari 11+
- ✅ Edge 79+
- ❌ Internet Explorer (не поддерживается)

## Безопасность

- Все операции с аудиоустройствами требуют разрешения пользователя
- Данные не передаются на сервер - обработка происходит локально
- Поддерживается только HTTPS в продакшене (требование браузеров)
